#!/usr/bin/env python3
"""
Battery Age Calculator

This script calculates the age of each battery that appears in the working vehicles and HV repair files.
The goal is to find the start date of each battery and calculate its age in days from today.

Data Sources:
- working_vehicles.csv (matching vehicles)
- working_unique_vehicles.csv (unique vehicles)
- hv_repair_2025-06-02b.csv (repair events)
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import sys
from typing import Dict, List, Tuple, Optional, Set, Any, TypedDict
from sqlalchemy import create_engine, text
import warnings

warnings.filterwarnings("ignore")

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("find_battery_age.log"),
    ],
)
logger = logging.getLogger(__name__)


class BatteryEvent(TypedDict):
    """
    Structure of a battery event as processed from repair data.

    Fields:
    - vin: Vehicle identification number where event occurred
    - date: Date when the event happened (datetime.date object)
    - column: Event type - "old" (removal), "new" (installation), or None (working-only vehicle)
    - event_id: Unique identifier for the repair record (DataFrame index or None for working-only)
    - row_data: Complete repair record data (pandas Series or None for working-only)
    """

    vin: str
    date: datetime.date  # Note: this is datetime.date, not datetime.datetime
    column: Optional[str]  # "old", "new", or None
    event_id: Optional[int]  # DataFrame index or None
    row_data: Optional[Any]  # pandas Series or None


class BatteryInterval(TypedDict):
    """
    Structure of a battery lifecycle interval (one residence period in a vehicle).

    Fields:
    - battery_id: Unique battery identifier
    - vin: Vehicle where battery resided during this interval
    - interval_start: Installation date (datetime.date)
    - interval_end: Removal date (datetime.date or None if still installed)
    - interval_type: Classification - "original", "replacement", "swap", "unknown"
    - source_event_ids: List of repair event IDs that created/closed this interval
    - confidence: Data quality score (0.0-1.0)
    - notes: Additional context or error information
    """

    battery_id: str
    vin: str
    interval_start: Optional[datetime.date]  # None for unknown start
    interval_end: Optional[datetime.date]  # None for currently installed
    interval_type: str  # "original", "replacement", "swap", "unknown"
    source_event_ids: List[Optional[int]]  # Event IDs that created this interval
    confidence: float  # 0.0-1.0 data quality score
    notes: str  # Additional context


class BatteryProcessor:
    """
    Processes lifecycle events for a single battery using state machine approach.

    This class tracks the complete lifecycle of one battery through multiple vehicles.
    It processes chronologically ordered events (installations/removals) and maintains:
    - One open_interval at most (current installation)
    - Multiple completed_intervals (past residence periods)

    State transitions:
    - "new" event: Installation → opens new interval (closes previous if exists)
    - "old" event: Removal → closes current interval
    - None event: Working-only vehicle → creates interval from erstzulassung
    """

    def __init__(self, battery_id: str):
        self.battery_id = battery_id
        self.open_interval = None  # Current active interval (if any)
        self.completed_intervals: List[BatteryInterval] = []  # List of closed intervals
        self.appearance_counter = 0  # Track appearance order for classification

    def process_event(self, event: BatteryEvent) -> None:
        """
        Process a single battery event (installation or removal).

        Args:
            event: BatteryEvent containing vin, date, column, event_id, and row_data
        """
        self.appearance_counter += 1

        if event["column"] == "new":  # Installation event
            self._handle_installation(event)
        elif event["column"] == "old":  # Removal event
            self._handle_removal(event)
        elif event["column"] is None:  # Working-only vehicle (erstzulassung)
            self._handle_working_only(event)

    def _handle_installation(self, event: BatteryEvent) -> None:
        """
        Handle battery installation event.

        Args:
            event: Installation event with column="new"
        """
        if self.open_interval:
            # Close previous interval (assume removal before installation)
            self._close_interval(
                event["date"],
                confidence=0.7,
                note="Missing removal - Auto-closed before new installation",
            )

        # Open new interval
        self.open_interval = {
            "vin": event["vin"],
            "start": event["date"],
            "open_src": event.get("event_id"),
            "type": self._classify_installation_type(),
            "battery_id": self.battery_id,
        }

    def _handle_removal(self, event: BatteryEvent) -> None:
        """
        Handle battery removal event.

        Args:
            event: Removal event with column="old"
        """
        if self.open_interval and self.open_interval["vin"] == event["vin"]:
            # Close current interval
            self._close_interval(event["date"], confidence=0.95)
        elif self.open_interval:
            # Removal from different vehicle - possible data error
            self._close_interval(
                event["date"],
                confidence=0.6,
                note=f"Data error - Removal from {event['vin']} but was installed in {self.open_interval['vin']} on {self.open_interval['start']}",
            )
        else:
            # Removal without installation - treat as retroactive installation
            self._handle_orphaned_removal(event)

    def _handle_working_only(self, event: BatteryEvent) -> None:
        """
        Handle battery from working-only vehicle (no repair history).

        Args:
            event: Working-only event with column=None
        """
        if not self.open_interval:
            # Create open interval starting from erstzulassung
            self.open_interval = {
                "vin": event["vin"],
                "start": event["date"],
                "open_src": None,
                "type": "original",
                "battery_id": self.battery_id,
                ""
            }

    def _handle_orphaned_removal(self, event: BatteryEvent) -> None:
        """
        Handle removal event without corresponding installation.

        Args:
            event: Orphaned removal event
        """
        # Create and immediately close interval (unknown start date)
        interval = {
            "battery_id": self.battery_id,
            "vin": event["vin"],
            "interval_start": None,  # Unknown start
            "interval_end": event["date"],
            "interval_type": "unknown",
            "source_event_ids": [event.get("event_id")],
            "confidence": 0.4,
            "notes": "Orphaned removal - Removal without installation record",
        }
        self.completed_intervals.append(interval)

    def _close_interval(
        self, end_date: datetime, confidence: float = 0.95, note: str = ""
    ) -> None:
        """Close the current open interval."""
        if not self.open_interval:
            return

        interval = {
            "battery_id": self.battery_id,
            "vin": self.open_interval["vin"],
            "interval_start": self.open_interval["start"],
            "interval_end": end_date,
            "interval_type": self.open_interval["type"],
            "source_event_ids": [self.open_interval["open_src"]],
            "confidence": confidence,
            "notes": note,
        }
        self.completed_intervals.append(interval)
        self.open_interval = None

    def _classify_installation_type(self) -> str:
        """
        Classify installation as original, replacement, or swap.

        Returns:
            str: "original" for first installation, "replacement" for subsequent ones
        """
        if self.appearance_counter == 1:
            return "original"
        else:
            return "replacement"

    def finalize(self) -> List[BatteryInterval]:
        """
        Finalize processing and return all intervals (including open ones).

        Returns:
            List[BatteryInterval]: Complete list of battery residence intervals
        """
        intervals = self.completed_intervals.copy()

        # Add open interval as ongoing
        if self.open_interval:
            interval: BatteryInterval = {
                "battery_id": self.battery_id,
                "vin": self.open_interval["vin"],
                "interval_start": self.open_interval["start"],
                "interval_end": None,  # Still active
                "interval_type": self.open_interval["type"],
                "source_event_ids": [self.open_interval["open_src"]],
                "confidence": 0.95,
                "notes": "Currently installed",
            }
            intervals.append(interval)

        return intervals


class BatteryAgeCalculator:
    """Calculate battery ages based on repair events and vehicle data."""

    def __init__(self):
        self.today = datetime.now().date()

        self.db_engine = None

        # Data containers
        self.hv_repair_df = None
        self.daily_stats_df = None
        self.working_vehicles_df = None
        self.working_unique_df = None

        # Processing results
        self.daily_stats_by_vehicle = {}  # Pre-indexed by vehicle_id for fast lookup
        self.battery_vehicles = {}  # battery_id -> list of battery appearance dicts
        self.vehicle_info = {}  # vin -> vehicle info
        self.vin_to_vehicle_id = {}
        self.unique_vehicles = set()
        self.unique_batteries = set()

        # Enhanced lifecycle tracking
        self.battery_processors = {}  # battery_id -> BatteryProcessor
        self.battery_timelines = []  # Complete interval records

        # Statistics
        self.stats = {
            "total_batteries": 0,
            "total_vehicles": 0,
            "working_only_vehicles": 0,
            "errors": [],
        }

    def _initialize_database_connection(self):
        host = os.getenv("DB_HOST", "localhost")
        port = os.getenv("DB_PORT", "6543")
        database = os.getenv("DB_NAME", "LeitwartenDB")
        user = os.getenv("DB_USER", "datadump")
        password = os.getenv("DB_PASSWORD", "pAUjuLftyHURa5Ra")
        db_connection_string = (
            f"postgresql://{user}:{password}@{host}:{port}/{database}"
        )
        try:
            self.db_engine = create_engine(db_connection_string)
            with self.db_engine.connect() as conn:
                conn.execute(text("SELECT 1"))
                logger.info("✅ Database connection established successfully")
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            logger.error(
                "Pipeline requires PostgreSQL connection for activity validation"
            )
            logger.error(f"Connection string: {db_connection_string}")
            logger.error("Please ensure PostgreSQL server is running and accessible")
            raise ConnectionError(f"Required database connection failed: {e}")

    def _load_vin_mappings(self):
        """Load VIN to vehicle_id mapping from database"""
        if not self.db_engine:
            logger.warning(
                "No database connection - cant not load VIN to vehicle_id mapping"
            )
            raise

        try:
            mapping_query = """
            SELECT vin, vehicle_id
            FROM public.vehicles 
            WHERE vin IS NOT NULL
            """

            mapping_df = pd.read_sql(mapping_query, self.db_engine)
            logger.info(
                f"Loaded VIN mapping for {len(mapping_df):,} vehicles from database"
            )

            # Build VIN to vehicle_id mapping
            for _, row in mapping_df.iterrows():
                vin = row["vin"]
                if pd.notna(vin):
                    self.vin_to_vehicle_id[vin] = row["vehicle_id"]

            logger.info(
                f"Built VIN to vehicle_id mapping for {len(self.vin_to_vehicle_id)} vehicles"
            )

        except Exception as e:
            logger.error(f"Failed to load VIN to vehicle_id mapping: {e}")
            logger.error(f"Error details: {type(e).__name__}: {str(e)}")
            raise

    def load_data(self):
        """Load all data files."""
        logger.info("Loading data files...")

        self._initialize_database_connection()

        # Load HV repair data
        logger.info("Loading HV repair data...")
        self.hv_repair_df = pd.read_csv("hv_repair_2025-06-02b.csv")
        logger.info(f"Loaded {len(self.hv_repair_df)} HV repair records")

        # Load working vehicles data
        logger.info("Loading working vehicles data...")
        self.working_vehicles_df = pd.read_csv(
            "comparison_results/working_matching_vehicles.csv"
        )
        self.working_unique_df = pd.read_csv(
            "comparison_results/working_unique_vehicles.csv"
        )
        self.daily_stats_df = pd.read_csv(
            "daily_stats.csv",
            dtype={"vehicle_id": "int", "km_start": "float", "km_end": "float"},
            parse_dates=["date"],
        )

        logger.info(f"Loaded {len(self.working_vehicles_df)} matching vehicles")
        logger.info(f"Loaded {len(self.working_unique_df)} unique vehicles")
        logger.info(f"Loaded {len(self.daily_stats_df)} daily stats records")

        # Combine working vehicles
        self.working_vehicles_df = pd.concat(
            [self.working_vehicles_df, self.working_unique_df], ignore_index=True
        )
        logger.info(f"Total working vehicles: {len(self.working_vehicles_df)}")

        logger.info("Loading VIN to vehicle_id mapping from database...")
        self._load_vin_mappings()

        logger.info("Pre-indexing daily stats by vehicle_id for fast lookup...")
        for vehicle_id, group in self.daily_stats_df.groupby("vehicle_id"):
            # Sort by date for each vehicle
            vehicle_data = group.sort_values("date").copy()
            self.daily_stats_by_vehicle[vehicle_id] = vehicle_data
        logger.info(
            f"Pre-indexed daily stats for {len(self.daily_stats_by_vehicle):,} vehicles"
        )
        logger.info(
            "Memory optimization: clear dailystats dataframe as we now have indexed data"
        )
        self.daily_stats_df = None

    def clean_data(self):
        """Clean and prepare data for processing."""
        logger.info("Cleaning data...")

        # Clean HV repair data
        self.hv_repair_df["created"] = pd.to_datetime(
            self.hv_repair_df["created"], errors="coerce"
        )
        self.hv_repair_df["battery_changed"] = self.hv_repair_df[
            "battery_changed"
        ].replace("--", None)
        self.hv_repair_df["battery_changed"] = pd.to_datetime(
            self.hv_repair_df["battery_changed"], errors="coerce"
        )

        # Create effective date (battery_changed if available, otherwise created)
        self.hv_repair_df["effective_date"] = self.hv_repair_df[
            "battery_changed"
        ].fillna(self.hv_repair_df["created"])

        # Clean battery IDs
        for col in ["battery_id_old", "battery_id_new"]:
            self.hv_repair_df[col] = self.hv_repair_df[col].astype(str)
            self.hv_repair_df[col] = self.hv_repair_df[col].replace(
                ["nan", "", " ", "None"], None
            )

        # Clean working vehicles data
        self.working_vehicles_df["erstzulassung"] = pd.to_datetime(
            self.working_vehicles_df["erstzulassung"], errors="coerce"
        )

        for col in ["master", "slave"]:
            if col in self.working_vehicles_df.columns:
                self.working_vehicles_df[col] = self.working_vehicles_df[col].astype(
                    str
                )
                self.working_vehicles_df[col] = self.working_vehicles_df[col].replace(
                    ["nan", "", " ", "None"], None
                )

        # Filter valid records
        self.hv_repair_df = self.hv_repair_df.dropna(subset=["vin", "effective_date"])
        self.working_vehicles_df = self.working_vehicles_df.dropna(subset=["vin"])

        logger.info(
            f"After cleaning: {len(self.hv_repair_df)} repair records, {len(self.working_vehicles_df)} working vehicles"
        )

    def process_hv_repair_data(self):
        """Process HV repair data to track battery appearances."""
        logger.info("Processing HV repair data...")

        # Sort by effective date to process chronologically. First event = first appearance = earliest seen date of batteries (old or new)
        self.hv_repair_df = self.hv_repair_df.sort_values("effective_date")

        for idx, row in self.hv_repair_df.iterrows():
            vin = row["vin"]
            effective_date = row["effective_date"].date()
            old_battery = row["battery_id_old"]
            new_battery = row["battery_id_new"]
            event_id = idx  # Use DataFrame index as event ID

            # Process old battery appearance
            if old_battery and pd.notna(old_battery):
                self.unique_batteries.add(old_battery)
                if old_battery not in self.battery_vehicles:
                    self.battery_vehicles[old_battery] = []
                self.battery_vehicles[old_battery].append(
                    {
                        "vin": vin,
                        "date": effective_date,
                        "column": "old",
                        "event_id": event_id,
                        "row_data": row,
                    }
                )
                logger.debug(
                    f"Battery {old_battery} appeared as old in vehicle {vin} on {effective_date}"
                )

            # Process new battery appearance
            if new_battery and pd.notna(new_battery):
                self.unique_batteries.add(new_battery)
                if new_battery not in self.battery_vehicles:
                    self.battery_vehicles[new_battery] = []
                self.battery_vehicles[new_battery].append(
                    {
                        "vin": vin,
                        "date": effective_date,
                        "column": "new",
                        "event_id": event_id,
                        "row_data": row,
                    }
                )
                logger.debug(
                    f"Battery {new_battery} appeared as new in vehicle {vin} on {effective_date}"
                )

            # Track vehicle
            self.unique_vehicles.add(vin)

        logger.info(f"Found {len(self.unique_batteries)} batteries from repair data")

    def _get_first_active_date_for_vin(self, vin: str) -> datetime:
        if vin not in self.vin_to_vehicle_id:
            logger.info(
                f"Cannot get first active date for {vin} - vehicle_id not found"
            )
            return None

        try:
            vehicle_id = self.vin_to_vehicle_id[vin]

            if vehicle_id not in self.daily_stats_by_vehicle:
                logger.info(
                    f"Cannot get first active date for {vin} and vehicle_id {vehicle_id} - no daily stats"
                )
                return None

            vehicle_data = self.daily_stats_by_vehicle[vehicle_id]

            if len(vehicle_data) > 0:
                first_date = vehicle_data["date"].min()
                if pd.notna(first_date):
                    return first_date.date()

            return None

        except Exception as e:
            logger.error(f"Error getting first active date for {vin}: {e}")
            return None

    def _get_last_active_date_for_vin(self, vin: str) -> datetime:
        if vin not in self.vin_to_vehicle_id:
            logger.info(f"Cannot get last active date for {vin} - vehicle_id not found")
            return None

        try:
            vehicle_id = self.vin_to_vehicle_id[vin]
            if vehicle_id not in self.daily_stats_by_vehicle:
                logger.info(
                    f"Cannot get last active date for {vin} and vehicle_id {vehicle_id} - no daily stats"
                )
                return None

            vehicle_data = self.daily_stats_by_vehicle[vehicle_id]

            if len(vehicle_data) > 0:
                last_date = vehicle_data["date"].max()
                if pd.notna(last_date):
                    return last_date.date()
            return None

        except Exception as e:
            logger.error(f"Error getting last active date for {vin}: {e}")
            return None

    def build_vehicle_info(self):
        """Build vehicle information from working vehicles data."""
        logger.info("Building vehicle information...")

        for _, row in self.working_vehicles_df.iterrows():
            vin = row["vin"]
            self.unique_vehicles.add(vin)
            first_active_date = self._get_first_active_date_for_vin(vin)
            self.vehicle_info[vin] = {
                "vin": vin,
                "erstzulassung": (
                    row.get("erstzulassung").date()
                    if pd.notna(row.get("erstzulassung"))
                    else first_active_date
                ),
                "first_active_date": first_active_date,
                "master_battery": row.get("master"),
                "slave_battery": row.get("slave"),
                "akz": row.get("akz"),
                "first_active_date": first_active_date,
                "last_active_date": self._get_last_active_date_for_vin(vin),
            }

        logger.info(f"Built info for {len(self.vehicle_info)} vehicles")

    def handle_vehicle_completeness(self):
        """Add batteries from working-only vehicles."""
        logger.info("Adding batteries from working-only vehicles...")

        repair_vins = set(self.hv_repair_df["vin"].unique())
        working_vins = set(self.vehicle_info.keys())

        # Add batteries from vehicles that only appear in working data
        working_only_vins = working_vins - repair_vins
        for vin in working_only_vins:
            self._add_working_only_batteries(vin)

        self.stats["working_only_vehicles"] = len(working_only_vins)
        logger.info(
            f"Added batteries from {len(working_only_vins)} working-only vehicles"
        )

    def _add_working_only_batteries(self, vin: str):
        """Add batteries from vehicles that only appear in working data."""
        vehicle_info = self.vehicle_info[vin]
        erstzulassung = vehicle_info["erstzulassung"]

        if pd.isna(erstzulassung):
            logger.warning(f"Vehicle {vin} has no erstzulassung date")
            return

        # Add batteries from working data
        for battery_field in ["master_battery", "slave_battery"]:
            battery_id = vehicle_info[battery_field]
            if battery_id and pd.notna(battery_id):
                self.unique_batteries.add(battery_id)
                if battery_id not in self.battery_vehicles:
                    self.battery_vehicles[battery_id] = []
                self.battery_vehicles[battery_id].append(
                    {
                        "vin": vin,
                        "date": erstzulassung,
                        "column": None,
                        "event_id": None,
                        "row_data": None,
                    }
                )
                logger.debug(
                    f"Battery {battery_id} from working-only vehicle {vin} with erstzulassung {erstzulassung}"
                )

    def calculate_ages(self):
        logger.info("Calculating battery ages...")

        self.battery_ages = []

        for battery_id in self.unique_batteries:
            if (
                battery_id not in self.battery_vehicles
                or not self.battery_vehicles[battery_id]
            ):
                # Battery with no appearances (shouldn't happen)
                logger.warning(f"Battery {battery_id} has no appearances")
                self.battery_ages.append(
                    {
                        "battery_id": battery_id,
                        "start_date": None,
                        "start_vehicle": None,
                        "age_in_days": None,
                        "note": "No appearances found",
                    }
                )
                continue

            # Find earliest appearance
            appearances = self.battery_vehicles[battery_id]
            earliest_appearance = min(appearances, key=lambda x: x["date"])
            earliest_vin = earliest_appearance["vin"]
            earliest_appear_date = earliest_appearance["date"]

            # Vehicles can not ride without batteries, so we extend battery start date to erstzulassung if available, otherwise use earliest repair date
            if earliest_vin in self.vehicle_info:
                vehicle_erstzulassung = self.vehicle_info[earliest_vin]["erstzulassung"]
                if pd.notna(vehicle_erstzulassung):
                    start_date = vehicle_erstzulassung
                    logger.debug(
                        f"Battery {battery_id}: Using erstzulassung {start_date} from vehicle {earliest_vin}"
                    )
                else:
                    start_date = earliest_appear_date
                    logger.debug(
                        f"Battery {battery_id}: Vehicle {earliest_vin} has no erstzulassung, using repair date {start_date}"
                    )
            else:
                start_date = earliest_appear_date
                logger.debug(
                    f"Battery {battery_id}: Vehicle {earliest_vin} not in working data, using repair date {start_date}"
                )

            # Calculate age
            age_days = (self.today - start_date).days

            # Create chronologically sorted unique vehicle list
            sorted_appearances = sorted(appearances, key=lambda x: x["date"])
            chronological_vehicles = []
            seen_vehicles = set()
            for app in sorted_appearances:
                if app["vin"] not in seen_vehicles:
                    chronological_vehicles.append(app["vin"])
                    seen_vehicles.add(app["vin"])

            self.battery_ages.append(
                {
                    "battery_id": battery_id,
                    "start_date": start_date,
                    "start_vehicle": earliest_vin,
                    "age_in_days": age_days,
                    "note": f"Associated with vehicles: {', '.join(chronological_vehicles)}",
                }
            )

        self.stats["total_batteries"] = len(self.unique_batteries)
        self.stats["total_vehicles"] = len(self.unique_vehicles)
        logger.info(f"Calculated ages for {len(self.battery_ages)} batteries")

    def build_battery_timelines(self):
        """Build complete battery lifecycle timelines using state machine approach."""
        logger.info("Building battery lifecycle timelines...")

        # Process each battery independently
        for battery_id in self.unique_batteries:
            if (
                battery_id not in self.battery_vehicles
                or not self.battery_vehicles[battery_id]
            ):
                logger.warning(f"Battery {battery_id} has no events")
                continue

            # Create processor for this battery
            processor = BatteryProcessor(battery_id)
            self.battery_processors[battery_id] = processor

            # Sort events chronologically with deterministic tie-breaking

            events = sorted(
                self.battery_vehicles[battery_id],
                key=lambda x: (
                    x["date"],
                    0 if x["column"] == "old" else 1,  # tie-breaker
                    x.get("event_id", 0),  # stable deterministic order
                ),
            )
            # Process events through state machine
            for event in events:
                processor.process_event(event)

            # Finalize and collect intervals
            intervals = processor.finalize()
            self.battery_timelines.extend(intervals)

        logger.info(
            f"Built timelines with {len(self.battery_timelines)} intervals for {len(self.battery_processors)} batteries"
        )

    def calculate_ages_from_timelines(self):
        """Calculate battery ages from complete timelines (alternative to existing method)."""
        logger.info("Calculating battery ages from timelines...")

        self.battery_ages_from_timelines = []

        for battery_id in self.unique_batteries:
            # Find all intervals for this battery
            battery_intervals = [
                i for i in self.battery_timelines if i["battery_id"] == battery_id
            ]

            if not battery_intervals:
                logger.warning(f"Battery {battery_id} has no intervals")
                continue

            # Find earliest start date across all intervals
            earliest_start = None
            for interval in battery_intervals:
                if interval["interval_start"] and (
                    earliest_start is None
                    or interval["interval_start"] < earliest_start
                ):
                    earliest_start = interval["interval_start"]

            if earliest_start is None:
                logger.warning(f"Battery {battery_id} has no valid start dates")
                continue

            # Calculate age
            age_days = (self.today - earliest_start).days

            # Build vehicle sequence
            sorted_intervals = sorted(
                battery_intervals,
                key=lambda x: x["interval_start"] or datetime.min.date(),
            )
            vehicle_sequence = [interval["vin"] for interval in sorted_intervals]

            # Determine current status
            active_intervals = [
                i for i in battery_intervals if i["interval_end"] is None
            ]
            if active_intervals:
                current_status = f"Currently in {active_intervals[0]['vin']}"
            else:
                current_status = "Not currently installed"

            self.battery_ages_from_timelines.append(
                {
                    "battery_id": battery_id,
                    "start_date": earliest_start,
                    "age_in_days": age_days,
                    "total_intervals": len(battery_intervals),
                    "vehicle_sequence": " → ".join(vehicle_sequence),
                    "current_status": current_status,
                    "timeline_confidence": min(
                        [i["confidence"] for i in battery_intervals]
                    ),
                }
            )

        logger.info(
            f"Calculated timeline-based ages for {len(self.battery_ages_from_timelines)} batteries"
        )

    def generate_outputs(self):
        """Generate output files."""
        logger.info("Generating output files...")

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Generate original CSV output (backward compatibility)
        df = pd.DataFrame(self.battery_ages)
        csv_filename = f"battery_age_results_{timestamp}.csv"
        df.to_csv(csv_filename, index=False)
        logger.info(f"Saved original results to {csv_filename}")

        # Generate enhanced timeline CSV output
        if hasattr(self, "battery_timelines") and self.battery_timelines:
            timeline_df = pd.DataFrame(self.battery_timelines)
            timeline_csv = f"battery_lifecycle_timelines_{timestamp}.csv"
            timeline_df.to_csv(timeline_csv, index=False)
            logger.info(f"Saved timeline data to {timeline_csv}")

        # Generate timeline-based ages CSV
        if (
            hasattr(self, "battery_ages_from_timelines")
            and self.battery_ages_from_timelines
        ):
            timeline_ages_df = pd.DataFrame(self.battery_ages_from_timelines)
            timeline_ages_csv = f"battery_ages_from_timelines_{timestamp}.csv"
            timeline_ages_df.to_csv(timeline_ages_csv, index=False)
            logger.info(f"Saved timeline-based ages to {timeline_ages_csv}")

        # Generate statistics file
        stats_filename = f"battery_age_statistics_{timestamp}.txt"
        with open(stats_filename, "w") as f:
            f.write("Battery Age Calculation Statistics\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"Processing Date: {datetime.now()}\n")
            f.write(f"Total Batteries: {self.stats['total_batteries']}\n")
            f.write(f"Total Vehicles: {self.stats['total_vehicles']}\n")
            f.write(f"Working-only Vehicles: {self.stats['working_only_vehicles']}\n")
            f.write(f"Errors: {len(self.stats['errors'])}\n")

            # Enhanced statistics
            if hasattr(self, "battery_timelines"):
                f.write(f"Total Timeline Intervals: {len(self.battery_timelines)}\n")
                active_intervals = len(
                    [i for i in self.battery_timelines if i["interval_end"] is None]
                )
                f.write(f"Currently Active Intervals: {active_intervals}\n")
                completed_intervals = len(self.battery_timelines) - active_intervals
                f.write(f"Completed Intervals: {completed_intervals}\n")

            if self.stats["errors"]:
                f.write("\nErrors:\n")
                for error in self.stats["errors"]:
                    f.write(f"- {error}\n")

        logger.info(f"Saved statistics to {stats_filename}")

        return csv_filename, stats_filename

    def run(self, enable_enhanced_timelines: bool = True):
        """Run the complete battery age calculation process."""
        logger.info("Starting battery age calculation...")

        try:
            self.load_data()
            self.clean_data()
            self.process_hv_repair_data()
            self.build_vehicle_info()
            self.handle_vehicle_completeness()

            # Original age calculation (for backward compatibility)
            self.calculate_ages()

            # Enhanced timeline processing (optional)
            if enable_enhanced_timelines:
                logger.info("Running enhanced timeline analysis...")
                self.build_battery_timelines()
                self.calculate_ages_from_timelines()

            csv_file, stats_file = self.generate_outputs()

            logger.info("Battery age calculation completed successfully!")
            logger.info(f"Results saved to: {csv_file}")
            logger.info(f"Statistics saved to: {stats_file}")

            if enable_enhanced_timelines:
                logger.info("Enhanced timeline analysis completed!")
                logger.info(f"Timeline intervals: {len(self.battery_timelines)}")
                logger.info(f"Batteries with timelines: {len(self.battery_processors)}")

            return csv_file, stats_file

        except Exception as e:
            logger.error(f"Error during processing: {e}")
            raise


if __name__ == "__main__":
    calculator = BatteryAgeCalculator()
    calculator.run()
