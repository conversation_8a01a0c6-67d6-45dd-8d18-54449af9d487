#!/usr/bin/env python3
"""
Test script to demonstrate the enhanced battery lifecycle tracking system.

This script shows how the new BatteryEvent and BatteryInterval types work,
and demonstrates the state machine processing for battery lifecycles.
"""

import sys
from datetime import datetime, date, timedelta
from typing import List

# Import our enhanced types and classes
from find_battery_age import BatteryEvent, BatteryInterval, BatteryProcessor


def create_sample_events() -> List[BatteryEvent]:
    """Create sample battery events to demonstrate the system."""
    
    # Sample events for battery BAT-123 moving through multiple vehicles
    events: List[BatteryEvent] = [
        # Initial installation in VIN-AAA
        {
            "vin": "VIN-AAA",
            "date": date(2025, 1, 15),
            "column": "new",
            "event_id": 1001,
            "row_data": None  # Would be pandas Series in real usage
        },
        
        # Removal from VIN-AAA
        {
            "vin": "VIN-AAA", 
            "date": date(2025, 4, 20),
            "column": "old",
            "event_id": 1045,
            "row_data": None
        },
        
        # Installation in VIN-BBB (same day as removal - common pattern)
        {
            "vin": "VIN-BBB",
            "date": date(2025, 4, 20),
            "column": "new", 
            "event_id": 1045,  # Same repair event
            "row_data": None
        },
        
        # Removal from VIN-BBB
        {
            "vin": "VIN-BBB",
            "date": date(2025, 7, 10),
            "column": "old",
            "event_id": 1089,
            "row_data": None
        }
    ]
    
    return events


def create_working_only_event() -> BatteryEvent:
    """Create a working-only vehicle event (no repair history)."""
    
    event: BatteryEvent = {
        "vin": "VIN-CCC",
        "date": date(2025, 1, 1),  # erstzulassung date
        "column": None,  # No repair column
        "event_id": None,  # No repair event
        "row_data": None
    }
    
    return event


def demonstrate_battery_lifecycle():
    """Demonstrate complete battery lifecycle processing."""
    
    print("=== Enhanced Battery Lifecycle Tracking Demo ===\n")
    
    # Create processor for battery BAT-123
    processor = BatteryProcessor("BAT-123")
    
    # Get sample events
    events = create_sample_events()
    
    print("Processing events chronologically:")
    print("-" * 50)
    
    # Process each event
    for i, event in enumerate(events, 1):
        print(f"Event {i}: {event['column']} in {event['vin']} on {event['date']}")
        processor.process_event(event)
        
        # Show current state
        if processor.open_interval:
            print(f"  → Open interval: {processor.open_interval['vin']} since {processor.open_interval['start']}")
        else:
            print(f"  → No open interval")
        print(f"  → Completed intervals: {len(processor.completed_intervals)}")
        print()
    
    # Finalize and get complete timeline
    intervals = processor.finalize()
    
    print("Final Timeline:")
    print("-" * 50)
    for i, interval in enumerate(intervals, 1):
        status = "ACTIVE" if interval["interval_end"] is None else "COMPLETED"
        end_str = "ongoing" if interval["interval_end"] is None else str(interval["interval_end"])
        
        print(f"Interval {i} [{status}]:")
        print(f"  Vehicle: {interval['vin']}")
        print(f"  Period: {interval['interval_start']} → {end_str}")
        print(f"  Type: {interval['interval_type']}")
        print(f"  Confidence: {interval['confidence']}")
        print(f"  Notes: {interval['notes']}")
        print()
    
    return intervals


def demonstrate_working_only_battery():
    """Demonstrate processing of working-only vehicle battery."""
    
    print("=== Working-Only Vehicle Demo ===\n")
    
    processor = BatteryProcessor("BAT-456")
    
    # Process working-only event
    event = create_working_only_event()
    print(f"Processing working-only battery in {event['vin']} from {event['date']}")
    processor.process_event(event)
    
    # Get timeline
    intervals = processor.finalize()
    
    print("\nTimeline:")
    print("-" * 30)
    for interval in intervals:
        print(f"Vehicle: {interval['vin']}")
        print(f"Start: {interval['interval_start']} (from erstzulassung)")
        print(f"Status: Currently installed")
        print(f"Type: {interval['interval_type']}")
        print()


def demonstrate_data_structures():
    """Show the exact structure of events and intervals."""
    
    print("=== Data Structure Examples ===\n")
    
    # Show BatteryEvent structure
    sample_event = create_sample_events()[0]
    print("BatteryEvent structure:")
    print("-" * 30)
    for key, value in sample_event.items():
        print(f"  {key}: {value} ({type(value).__name__})")
    print()
    
    # Show BatteryInterval structure
    processor = BatteryProcessor("DEMO-BAT")
    processor.process_event(sample_event)
    intervals = processor.finalize()
    
    if intervals:
        print("BatteryInterval structure:")
        print("-" * 30)
        for key, value in intervals[0].items():
            print(f"  {key}: {value} ({type(value).__name__})")
    print()


if __name__ == "__main__":
    try:
        # Run demonstrations
        demonstrate_data_structures()
        demonstrate_battery_lifecycle()
        demonstrate_working_only_battery()
        
        print("✅ Enhanced battery tracking demonstration completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during demonstration: {e}")
        sys.exit(1)
